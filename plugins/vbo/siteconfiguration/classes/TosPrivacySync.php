<?php

namespace Vbo\SiteConfiguration\Classes;

use Exception;
use Http;
use Log;
use Vbo\SiteConfiguration\Models\SiteConfigSettings;

class TosPrivacySync
{
    protected $sourceUrls = [
        'besteleads' => 'https://www.besteleads.nl/get_avg_settings',
        'hypotheek' => 'https://www.hypotheekconnect.nl/get_avg_settings',
    ];

    protected $timeout = 10;

    protected $connectTimeout = 5;

    public function sync()
    {
        // Check if popup is enabled (handle null/empty values)
        $popupEnabled = SiteConfigSettings::get('tos_privacy_popup');
        if (! $popupEnabled) {
            Log::debug('TosPrivacySync: Skipping sync (popup disabled)');

            return false;
        }

        // Get the source type (handle null/empty values)
        $source = SiteConfigSettings::get('tos_privacy_source');

        // Skip if no source or custom source
        if (empty($source) || $source === 'custom') {
            Log::debug('TosPrivacySync: Skipping sync (source: '.($source ?: 'empty').')');

            return false;
        }

        // Validate source
        if (! isset($this->sourceUrls[$source])) {
            Log::error("TosPrivacySync: Invalid source '{$source}'");

            return false;
        }

        try {
            // Fetch data from external source
            $data = $this->fetchExternalData($source);

            if ($data === false) {
                return false;
            }

            // Save the data
            $this->saveData($data);

            // Update last sync timestamp
            SiteConfigSettings::set('tos_privacy_last_sync', now()->toDateTimeString());

            // Log::info("TosPrivacySync: Successfully synced content from '{$source}'");

            return true;

        } catch (Exception $e) {
            Log::error('TosPrivacySync: Failed to sync content - '.$e->getMessage());

            return false;
        }
    }

    protected function fetchExternalData($source)
    {
        $url = $this->sourceUrls[$source];

        try {
            $response = Http::timeout($this->timeout)
                ->connectTimeout($this->connectTimeout)
                ->get($url);

            if ($response->failed()) {
                Log::error("TosPrivacySync: HTTP request failed for '{$source}' (Status: {$response->status()})");

                return false;
            }

            $data = json_decode($response->body(), true);

            if (! $data) {
                Log::error("TosPrivacySync: Invalid JSON response from '{$source}'");

                return false;
            }

            return $data;

        } catch (Exception $e) {
            Log::error("TosPrivacySync: Exception while fetching from '{$source}' - ".$e->getMessage());

            return false;
        }
    }

    /**
     * Save TOS/Privacy data to settings
     *
     * @param array $data
     * @return void
     */
    protected function saveData($data)
    {
        // Save the complete raw data from external source
        SiteConfigSettings::set('tos_privacy_synced', $data);
    }
}
