function onFormSend($form) {
  if ( $form.hasClass('prelander') ) {
    $form[0].reset();
    $('#contact-form-2').hide();

    if (window.grecaptcha) {
        grecaptcha.reset();
    }

    $('.upload-files-container').html('');

    $('.responsiv-uploader-fileupload').removeClass('is-populated');
  } else {
    if ( !$('html').hasClass('oc-boxes-edit-mode')) {
        $form.hide();
        $('html, body').animate({
            scrollTop: $("#form_default").offset().top -16
        }, 300);
    }
  }
}
function onPartnerFormSend($form) {
    $form.hide();
    $('#partner_form_title').hide();
}
function scrollToMain(hasSlogan) {
  $('html:not(.oc-boxes-edit-mode), body').animate({
    scrollTop: $("#form_default").offset().top - (hasSlogan ? 50 : 16)
  }, 150);
}

function isNumberKey(evt)
{
    var charCode = (evt.which) ? evt.which : event.keyCode;
    if (charCode != 46 && charCode != 45 && charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    }

    return true;
}

jQuery(function() {

    var tooltipicon = $('.form-hor-label').find('.fa-circle-info');
    tooltipicon.removeClass('fa-circle-info').addClass('fa-square-info');
    var formlang = $('#form_default form').data('countrycode');
    var zipcodeMin = 6;

    if ( formlang == 'be' || formlang == 'be-fr' ) {
        var zipcodeMin = 4;
    }
    if ( formlang == 'de' ) {
        var zipcodeMin = 5;
    }
    if ( formlang == 'nl' ) {
        var zipcodeMin = 6;
    }

    $('#contact-form-2 input[name="telefoonnummer"]').on('keyup', function(){
        var val = $(this).val();
        if ( val.length > 9 ) {
            $(this).addClass('validate peer');
        } else {
            $(this).removeClass('validate peer');
        }
      });

  var form = $('#form_default form');
  form.data('active_step', 'form-step-1');
  form.attr('data-active_step', 'form-step-1');

  // Track user form interaction for Alpine.js
//   $('#form_default form').on('focus input change', 'input, select, textarea', function() {
//     // Dispatch event to update userFormInteraction in parent Alpine.js context
//     document.body.dispatchEvent(new CustomEvent('userFormInteraction'));
//   });

  form.find('fieldset').each(function(i, el){
    if (i !== null ) {
      form.find('.steps-wrap').show();
      var num = (i+1);

      $(this).data('stap', 'Stap '+ num);
      $(this).attr('data-stap', 'Stap '+ num);

      if (num === 1) {
        form.find('.steps').append('<div data-num="'+ num +'" data-name="form-step-'+ num +'" class="step active current">'+ num +'<span class="sr-only">Stap '+ num +'</span></div>');
      } else {
        form.find('.steps').append('<div data-num="'+ num +'" data-name="form-step-'+ num +'" class="step">'+ num +'<span class="sr-only">Stap '+ num +'</span></div>');

      }

      num++;

    }
  });
  $('#form_default').find('button[type="submit"].form-btn').on('click', function(e){
    var current_step = $(this).closest('fieldset');
    var validStep = true;

    current_step.find('[required],[required="required"]').each(function(i, input){
        var val = $(input).val();
        var name = $(input).attr('data-name');

        if ( val == '') {
            validStep = false;
            $(input).addClass('invalid');

            if ( $(input).prop('type') == 'radio' || $(input).attr('type') == 'checkbox' ) {
                $(input).closest('label').addClass('invalid');
            }
            if ( $(input).prop('type') == 'file' ) {
                $(input).closest('.upload-form-group').addClass('invalid');
            }
        } else if ( $(input).hasClass('field-int-phone') ) {
            if ( val.length < 4 ) {
                validStep = false;
                $(input).addClass('invalid');
            }
        } else if ( name == 'zipcode' ) {
            if ( val.length < zipcodeMin ) {
                validStep = false;
                $(input).addClass('invalid');
            }
        } else {
            $(input).addClass('valid');

            if ( $(input).prop('type') == 'radio' || $(input).attr('type') == 'checkbox' ) {
                $(input).closest('label').removeClass('invalid');
            }
            if ( $(input).prop('type') == 'file' ) {
                $(input).closest('.upload-form-group').removeClass('invalid');
            }
        }
    });

    var checkboxlist = current_step.find('.checkboxlist-required');
    if ( checkboxlist.length > 0 ) {
        if ( $('.checkboxlist-required :checkbox:checked').length == 0 ) {
            $('.checkboxlist-required .checkbox').addClass('not-valid');
            validStep = false;
        }
    }

    if ( !validStep ) {
        e.preventDefault();
        var firstInput = current_step.find('.invalid').first();
        firstInput.focus();
    } else {
      if ( $('[name="telefoonnummer"]') ) {
        $('[name="telefoonnummer"]').attr('disabled', false).prop('disabled', false);
      }
    }
  });
  $('.form-btn-next').on('click', function(e){
    e.preventDefault();

    var form = $(this).closest('form');
    var current_step = $(this).closest('fieldset');
    var next_step = current_step.next();
    var validStep = true;

    current_step.find('[required],[required="required"]').each(function(i, input){
        var val = $(input).val();
        var inputValid = input.checkValidity();

        if ( val == '' || !inputValid ) {
            validStep = false;
            $(input).addClass('invalid');
            if ( $(input).prop('type') == 'radio' || $(input).attr('type') == 'checkbox' ) {
                $(input).closest('label').addClass('invalid');
            }
            if ( $(input).prop('type') == 'file' ) {
                $(input).closest('.upload-form-group').addClass('invalid');
            }
        } else if ( $(input).hasClass('field-int-phone') ) {
            if ( val.length < 4 ) {
                validStep = false;
                $(input).addClass('invalid');
            }
        } else {
            $(input).addClass('valid');
            if ( $(input).prop('type') == 'radio' || $(input).attr('type') == 'checkbox' ) {
            $(input).closest('label').removeClass('invalid');
            }
            if ( $(input).prop('type') == 'file' ) {
                $(input).closest('.upload-form-group').removeClass('invalid');
            }
        }
    });

    var checkboxlist = current_step.find('.checkboxlist-required');
    if ( checkboxlist.length > 0 ) {
        if ( $('.checkboxlist-required :checkbox:checked').length == 0 ) {
            $('.checkboxlist-required .checkbox').addClass('not-valid');
            validStep = false;
        }
    }

    if ( !validStep ) {
        var firstInput = current_step.find('input.invalid').first();
        firstInput.focus();
    }

    if ( validStep ) {
      nextStep(current_step, next_step);
    }
  });

    function nextStep(current_step, next_step) {
        current_step.hide();
        next_step.show();

        var next_step_id = next_step.attr('id');
        var current_step_name = current_step.attr('data-stap');
        var next_step_name = next_step.attr('data-stap');
        var steps = current_step.closest('form').find('.steps');
        var next_step_orb = steps.find('[data-name="'+ next_step_id +'"]');
        var current_step_orb = steps.find('.active');
        var hasSlogan = $('#form_default').closest('#form_wrapper').hasClass('formHasTitleAbove');

        if ( next_step_orb.hasClass('completed') ) {
            next_step_orb.addClass('active');
        } else {
            next_step_orb.addClass('active').addClass('current');
        }

        current_step_orb.removeClass('active').removeClass('current').addClass('completed');

        var form = $('#form_default form');
        form.data('active_step', next_step_id);
        form.attr('data-active_step', next_step_id);

        checkOfferItems();

        if(window.innerWidth < 768) {
            scrollToMain(hasSlogan);
        }

        dataLayer.push({
            'event': 'leadformulierStappen', // vaste waarde
            'eventAction': next_step_name, // zichtbaar, stap 1, stap 2, stap 3 of 'afgerond'
            'eventLabel': next_step.closest('form').data('formname') // naam van product (traprenovatie, gietvloer etc.)
        });

    }

    $('.form-btn-prev').on('click', function(){
        var current_step = $(this).closest('fieldset');
        var prev_step = current_step.prev();
        var steps = current_step.closest('form').find('.steps');
        var prev_step_id = prev_step.attr('id');
        var prev_step_orb = steps.find('[data-name="'+ prev_step_id +'"]');
        var current_step_orb = prev_step_orb.next();
        var hasSlogan = $('#form_default').closest('#form_wrapper').hasClass('formHasTitleAbove');

        current_step.hide();
        prev_step.show();
        prev_step_orb.addClass('active');
        current_step_orb.removeClass('active');

        if ( current_step_orb.hasClass('completed') ) {
            current_step_orb.removeClass('current');
        }

        var form = $('#form_default form');
        form.data('active_step', prev_step_id);
        form.attr('data-active_step', prev_step_id);

        if(window.innerWidth < 768) {
            scrollToMain(hasSlogan);
        }
    });

    $('form').on('change', '.form-hor-field.invalid', function(){
        var val = $(this).val();
        if (val != '') {
            $(this).removeClass('')
        }
    });

    $('form').on('change', '.form-hor-radio.invalid', function(){
        var val = $(this).val();
        if (val != '') {
            $(this).closest('.form-hor-radiolist').find('label').removeClass('invalid');
        }
    });

    function addToOffer(value,title,name) {
        $('#offer_example_items').append(`
            <div class="mb-2 text-sm text-gray-800" id="`+ name +`">
            <div class="font-bold offer-title">`+ title +`</div>
            <div class="offer-value">`+ value +`</div>
            </div>`);
    }
    function changeOffer(value,title,name) {
        var offer_item = $('#offer_example_items').find('#'+ name +'');
        var offer_value = offer_item.find('.offer-value');

        offer_value.text(value);
    }
    function removeFromOffer(name) {
        var offer_item = $('#offer_example_items').find('#' + name);

        if ( offer_item ) {
            offer_item.remove();
        }
    }

    function checkOfferItems() {
        $('#form_default').find('.offer-item').each(function(i,e){
            var val = $(this).val();
            if ( $(this).hasClass('form-hor-range-field') ) {
                var val = $(this).closest('.form-hor-wrap').find('.form-hor-range-value-overlay span').text();
            }
            var title = $(this).data('title');
            var name = $(this).attr('name');
            if ( $(this).hasClass('form-hor-radiolist') ) {
                var name = $(this).data('name');
                var checkedInput = $(this).find(":checked");
                if ( checkedInput ) {
                    var val = checkedInput.val()
                }
            }
            if ( $(this).is('select') ) {
                var selectedInput = $(this).find(":selected");
                if ( selectedInput ) {
                    var val = selectedInput.text()
                }
            }
            var offer_item = $('#offer_example_items').find('#'+ name +'');
            var offer_item_fs = e.closest('fieldset');
            var offer_item_fs_data = $(offer_item_fs).attr('id');
            var form = e.closest('form');
            var current_fs = $(form).data('active_step');

            if (val) {
                if (offer_item.length > 0) {
                    changeOffer(val,title,name);
                } else {
                    if ( current_fs == offer_item_fs_data ) {
                        addToOffer(val,title,name);
                    }
                }
            }
        });
    }

    checkOfferItems();


    $('#form_default').on('change', '.offer-item', function(){
        var val = $(this).val();

        if ( $(this).hasClass('form-hor-range-field') ) {
            var val = $(this).closest('.form-hor-wrap').find('.form-hor-range-value-overlay span').text();
        }
        var title = $(this).data('title');
        var name = $(this).attr('name');
        if ( $(this).hasClass('form-hor-radiolist') ) {
            var checkedInput = $(this).find(":checked");
            var name = $(this).data('name');
            if ( checkedInput ) {
                var val = checkedInput.val()
            }
        }
        if ( $(this).is('select') ) {
            var selectedInput = $(this).find(":selected");
            if ( selectedInput ) {
                var val = selectedInput.text()
            }
        }
        var offer_item = $('#offer_example_items').find('#'+ name +'');
        if (offer_item.length > 0) {
            changeOffer(val,title,name);
        } else {
            addToOffer(val,title,name);
        }
    });

    $('#form_default').on('change','.checkboxlist-required .checkbox.not-valid input', function(){
        if ( $(this).closest('.checkboxlist-required').find(':checkbox:checked').length > 0 ) {
            $(this).closest('.checkboxlist-required').find('.checkbox').removeClass('not-valid');
        }
    });

    $('#form_default').on('change', '.form-hor-range-value', function(){
        $(this).closest('.form-hor-wrap').find('.form-hor-range-field').trigger('change');
    });

    var formDropdowns = $('select.form-hor-field');
    formDropdowns.each(function(i,e){
        var val = $(e).find('option:selected').val();

        if (val != '') {
            $(e).closest('.form-group').find('.form-hor-select-icon-svg').addClass('valid');
        }
    });

    $('#form_default').on('change', 'select.form-hor-field', function(){
        var val = $(this).find('option:selected').val();

        if (val != '') {
            $(this).closest('.form-group').find('.form-hor-select-icon-svg').addClass('valid');
        }
    });
    $('#form_default').on('change', '.invalid', function(){
        var val = $(this).val();
        if (val != '') {
            $(this).removeClass('invalid');
        }
    });

    $('.steps .step').on('click', function(){
        var dest = $(this).data('name');
        var dest_orb = $(this);
        var dest_num = $(this).data('num');
        var dest_step = $(this).closest('form').find('fieldset#'+ dest);
        var current = $('.steps .active').data('name');
        var current_orb = $('.steps .active');
        var current_num = $('.steps .active').data('num');
        var current_step = $(this).closest('form').find('fieldset#'+ current);

        var dest_diff = dest_num - current_num;
        if ( dest_diff == "1" || dest_orb.hasClass('current') ) {

            var validStep = true;
            current_step.find('[required],[required="required"]').each(function(i, input){
                var val = $(input).val();
                var name = $(input).attr('data-name');

                if ( val == '') {
                    validStep = false;
                    $(input).addClass('invalid');
                } else if ( name == 'zipcode' ) {
                    if ( val.length < zipcodeMin ) {
                        validStep = false;
                        $(input).addClass('invalid');
                    }
                } else {
                    $(input).addClass('valid');
                }
            });

            if ( validStep ) {
                nextStep(current_step, dest_step);
            }

        }

        if ( dest_num < current_num ) {
            current_step.hide();
            dest_step.show();
            dest_orb.addClass('active');
            current_orb.removeClass('active');
            if ( current_orb.hasClass('completed') ) {
                current_orb.removeClass('current');
            }
        }
  });

    $('[name="dobDay"]').attr('inputmode', 'numeric');
    $('[name="dobMonth"]').attr('inputmode', 'numeric');
    $('[name="dobYear"]').attr('inputmode', 'numeric');

    $('.form-lang-us [name="postcode"]').attr('inputmode', 'numeric');

    $('[name="dobDay"]').on('keyup', function(){
        var val = $(this).val();
        if ( val.length > 1 ) {
            $(this).trigger('change').blur();
            $('[name="dobMonth"]').focus();
        }
    });
    $('[name="dobDay"]').on('keydown', function(e){
        var val = $(this).val();
        if ( val.length > 1 && e.keyCode != 8 ) {
            return false;
        }
    });
    $('[name="dobDay"]').on('change', function(){
        var val = $(this).val();
        var num = parseFloat(val);
        if ( num > 31 ) {
            $(this).val(31);
        }
    });

    $('[name="dobMonth"]').on('keyup', function(){
        var val = $(this).val();
        if ( val.length > 1 ) {
            $(this).trigger('change').blur();
            $('[name="dobYear"]').focus();
        }
    });
    $('[name="dobMonth"]').on('keydown', function(e){
        var val = $(this).val();
        if ( val.length > 1 && e.keyCode != 8 ) {
            return false;
        }
    });
    $('[name="dobMonth"]').on('change', function(){
        var val = $(this).val();
        var num = parseFloat(val);
        if ( num > 12 ) {
            $(this).val(12);
        }
    });

    $('[data-name="zipcode"]').on('change', function(){
        var val = $(this).val();
        var trimmed = $.trim(val);
        $(this).val(trimmed);
    });

    $('.form-group.date_field input').on('keyup', function(e){
        var val = $(this).val();
        var str = val.length;

        if ( e.key != 'Backspace' ) {
            if ( str == 2 || str == 5  ) {
                $(this).val( val + '-');
            }
        }
    });

    $('.form-group.date_field input').on('keydown', function(e){
        if ( e.key == '-' || e.code == 'Space' ) {
            e.preventDefault();
        }
    });

    $('.form-group.date_field input').on('change', function(e){
        var val = $(this).val();
        var str = val.length;
        var splitDate = val.split('-');
        var day = parseFloat(splitDate[0]);
        var month = parseFloat(splitDate[1]);
        var year = parseFloat(splitDate[2]);

        if (day > 31) {
            validateDob(this, 'd');
        }
        if (month > 12) {
            validateDob(this, 'm');
        }

        if ( str < 10 || str > 10) {
            $(this).closest('.form-group').find('.dobInvalidYear').show();
        } else {
            $(this).closest('.form-group').find('.dobInvalidYear').hide();
        }
    });

    function validateDob(el, period) {
        var val = $(el).val();
        var splitDate = val.split('-');
        var day = parseFloat(splitDate[0]);
        var month = parseFloat(splitDate[1]);
        var year = parseFloat(splitDate[2]);

        setTimeout(function() {
            if (period === 'd') {
                $(el).val('31' + '-' + month + '-' + year);
            }
            if (period === 'm') {
                $(el).val(day + '-' + '12' + '-' + year);
            }
        }, 150);

        $(el).trigger('change');
    }

    $('.form-hor-range .form-hor-range-value').on('keyup', function(){
        var val = $(this).val();
        var formatted = val.replace(',', '').replace('.', '');
        $(this).val(formatted);
    });

    $('input[name="dobYear"]').on('change', function(){
        var val = $(this).val();

        if ( val.length < 4 || val.length > 4 ) {
            $(this).addClass('validate');
            $(this).closest('.form-group').find('.dobInvalidYear').show();
        } else {
            $(this).removeClass('validate');
            $(this).closest('.form-group').find('.dobInvalidYear').hide();
        }
    });

    $('.form-hor-range-value').on('focus', function(){
        var val = $(this).val();
        if(window.innerWidth < 768) {
            $(this).val('');
        }
    });

    $('.newsletter-field:not([name="postcode"])').attr('tabIndex', '-1');

    var prelander = $('.form_prelander form').find('fieldset');
    var lastfield = prelander.find('.form-group').last();
    var laststep = $('.form_prelander form').find('fieldset').last();
    laststep.addClass('prelander-last-step');

    prelander.each(function(i,e){
        $(e).find('.form-group').last().addClass('last-field');
    });

    prelander.find('input').on('change', function(){
        var current_field = $(this).closest('.form-group');
        var current_step = $(this).closest('fieldset');
        var next_step = current_step.next();

        if ( $(current_field).hasClass('last-field') && !$(current_step).hasClass('prelander-last-step') ) {
            setTimeout(function() {
                nextStep(current_step, next_step);
            }, 350);
        }
    });

});

$('.newsletter-wrapper').hide();

$('#form_default [data-name="emailaddress"]').on('change', function(){
    var $email = $(this).val();
    // $.request('onCheckMail', {
    //     data: {
    //         email: $email
    //     },
    //     success: function(data) {
    //         $('[data-name="validmail"]').val(data.result);
    //     }
    // });
    $('.newsletter-wrapper').slideDown();
  });

  $('#form_default [data-name="housenumber"]').on('change', function(){
    var val = $(this).val();
    var addition = $('[data-name="addition"]');
    var chars = val.split('');
    var numbers = '';
    var letters = '';

    $.each(chars, function(i,v){
      if ( !$.isNumeric(v) ) {
        letters = letters + v;
      } else {
        numbers = numbers + v;
      }
    });

    $(this).val(numbers);
    addition.val(letters);
  });

// NAT berekening

$('#form_default form.nat-form').on('change', '[name="partner"]', function(){
  var val = $('[name="partner"]:checked').val();
  var dobPartner = $('[name="geboortedatum_partner"]');

  if ( val == 0 || val == "Nee" || val == "nee" ) {
    dobPartner.val('');
    $('.bruto-maandinkomen_partner').hide();
    $('.pensioeninkomen-excl_partner').hide();
    $('.pensioeninkomen-incl_partner').hide();
  } else {
    if ( dobPartner.val().length > 0 ) {
      dobPartner.trigger('change');
    }
  }
});

$('#form_default').on('keypress', '[name="geboortedatum"], [name="geboortedatum_partner"]', function(){
    return isNumberKey(event)
});
$('#form_default form.nat-form').on('keyup', '[name="geboortedatum"], [name="geboortedatum_partner"]', function(){
    var val = $(this).val();
    if ( val.length == 10 ) {
        $(this).trigger('change').blur();
    }
});

$('#form_default form:not(.nat-form)').on('change', '[name="geboortedatum"], [name="geboortedatum_partner"]', function(){
    var $dob = $(this).val();
    var val = $(this).val();
    var dobNumbers = val.replace(/-/g, "");
    var $fieldName = $(this).prop('name');

    if ( $dob.length == 10 && !isNaN(dobNumbers) ) {
        $.request('onCalculateAge', {
            data: {
                date: $dob
            },
            success: function(data) {
                var age = parseInt(data.result);
                $('[name="'+ $fieldName +'_age"]').val(age);
            }
        });
    }
});

$('#form_default form.nat-form').on('change', '[name="geboortedatum"], [name="geboortedatum_partner"]', function(){
    var $dob = $(this).val();
    var val = $(this).val();
    var dobNumbers = val.replace(/-/g, "");
    var $fieldName = $(this).prop('name');
    var isNat = false;
    var partner = false;
    if ( $(this).prop('name') == "geboortedatum_partner" ) {
        partner = true;
    }

    if ( $dob.length == 10 && !isNaN(dobNumbers) ) {
        $.request('onCalculateAge', {
            data: {
                date: $dob
            },
            success: function(data) {
                var age = parseInt(data.result);
                $('[name="'+ $fieldName +'_age"]').val(age);

                if ( partner ) {
                    if ( age <= 55 ) {
                        $('.bruto-maandinkomen_partner').show();
                        $('.pensioeninkomen-excl_partner').hide();
                        $('.pensioeninkomen-incl_partner').hide();
                        $('.broninkomen_partner').show();
                    } else if ( age > 55 && age < 67 ) {
                        $('.bruto-maandinkomen_partner').hide();
                        $('.pensioeninkomen-excl_partner').show();
                        $('.pensioeninkomen-incl_partner').hide();
                        $('#broninkomen_partner').val('Pensioen').prop('required', false).trigger('change');
                    } else if ( age >= 67 ) {
                        $('.bruto-maandinkomen_partner').hide();
                        $('.pensioeninkomen-excl_partner').hide();
                        $('.pensioeninkomen-incl_partner').show();
                        $('#broninkomen_partner').val('Pensioen').prop('required', false).trigger('change');
                    }
                } else {
                    if ( age <= 55 ) {
                        $('.bruto-maandinkomen').show();
                        $('.pensioeninkomen-excl').hide();
                        $('.pensioeninkomen-incl').hide();
                        $('#broninkomen option[value="Pensioen"]').remove();
                        $('.broninkomen').show();
                    } else if ( age > 55 && age < 67 ) {
                        $('.bruto-maandinkomen').hide();
                        $('.pensioeninkomen-excl').show();
                        $('.pensioeninkomen-incl').hide();
                        $('#broninkomen').find('option[value="Pensioen"]').remove();
                        $('.broninkomen').show();
                    } else if ( age >= 67 ) {
                        $('.bruto-maandinkomen').hide();
                        $('.pensioeninkomen-excl').hide();
                        $('.pensioeninkomen-incl').show();
                        $('#broninkomen').closest('.form-group').hide();
                        // $('#broninkomen').append('<option value="Pensioen">Pensioen</option>');
                        $('#broninkomen').prop('required', false).trigger('change');
                    }
                }
            }
        });
    }
});

$('[data-name="upload-field"]').on('change', function(){
    var fileList = this.files;
    var uploadedFilesList = $(this).closest('.form-group').find('ul.upload-file-list');
    var wrapper = $(this).closest('.form-group');

    if ( wrapper.hasClass('invalid') ) {
        wrapper.closest('.upload-form-group').removeClass('invalid');
    }

    uploadedFilesList.empty();
    if ( fileList.length > 0 ) {
        $(this).closest('.form-group').find('button.remove-uploads').show();
    } else {
        $(this).closest('.form-group').find('button.remove-uploads').hide();
    }
    if ( fileList.length > 0 ) {
        $.each(fileList, function(idx,obj){
            var name = obj.name;
            var shortName = obj.name;
            if ( name.length > 50 ) {
                shortName = $.trim(obj.name).substring(0, 50).split(" ").slice(0, -1).join(" ") + "...";
            }
            uploadedFilesList.append('<li class="uploaded-file" data-index=' + idx + '><div class="file-name" title="' + name + '">' + shortName + '</div></li>');
        });
    }
});
$('.remove-uploads').on('click', function(){
    var input = $(this).closest('.form-group').find('input[data-name="upload-field"]');
    var uploadedFilesList = $(this).closest('.form-group').find('ul.upload-file-list');
    input.value = '';
    uploadedFilesList.empty();
    $(this).hide();
});

// window.document.addEventListener('offline.boxes.editorRefreshed', function (e) {
//     window.location.reload();
// });

var zipcodeField = $('[data-name="zipcode"]');
if ( zipcodeField.length > 0 ) {
    var zipcodeLang = zipcodeField.closest('form').data('countrycode');
    if ( zipcodeLang == 'be' || zipcodeLang == 'be-fr' || zipcodeLang == 'de' ) {
        zipcodeField.addClass('form-hor-field-suggestions');
        zipcodeField.attr('list', 'zipcodeSuggestions');
    }
    if ( zipcodeLang == 'be' || zipcodeLang == 'be-fr' ) {
        zipcodeField.attr('minlength', '4');
        zipcodeField.attr('pattern', '[0-9]{4,}');
        zipcodeField.attr(':class', "{'validate peer': zipcode !== null && zipcode.length >= 4}");
    }
    if ( zipcodeLang == 'de' ) {
        zipcodeField.attr('minlength', '5');
        zipcodeField.attr('pattern', '[0-9]{5,}');
        zipcodeField.attr(':class', "{'validate peer': zipcode !== null && zipcode.length >= 5}");
    }
    if ( zipcodeLang == 'nl' ) {
        zipcodeField.attr('minlength', '6');
        zipcodeField.attr('pattern', '[1-9][0-9]{3} ?(?!sa|sd|ss)[a-zA-Z]{2}');
        zipcodeField.attr(':class', "{'validate peer': zipcode !== null && zipcode.length >= 6}");
    }
}
var cityField = $('[data-name="city"]');
if ( cityField.length > 0 ) {
    var cityLang = cityField.closest('form').data('countrycode');
    if ( cityLang == 'be' || cityLang == 'be-fr' || cityLang == 'de' ) {
        cityField.addClass('form-hor-field-suggestions');
        cityField.attr('list', 'citySuggestions');
    }
}
var streetField = $('[data-name="streetname"]');
if ( streetField.length > 0 ) {
    var streetLang = streetField.closest('form').data('countrycode');
    if ( streetLang == 'be' || streetLang == 'be-fr' || streetLang == 'de' ) {
        streetField.addClass('form-hor-field-suggestions');
        streetField.attr('list', 'streetSuggestions');
    }
}

var housenumberField = $('[data-name="housenumber"]');
if ( housenumberField.length > 0 ) {
    var housenumberLang = housenumberField.closest('form').data('countrycode');
    if ( housenumberLang == 'be' || housenumberLang == 'be-fr' || housenumberLang == 'de' ) {
        housenumberField.addClass('form-hor-field-suggestions');
        housenumberField.attr('list', 'housenumberSuggestions');
    }
}
var newsletterZipcodeField = $('[data-name="zip_news"]');
if ( newsletterZipcodeField.length > 0 ) {
    var newsletterZipcodeLang = newsletterZipcodeField.closest('form').data('countrycode');
    if ( newsletterZipcodeLang == 'be' || newsletterZipcodeLang == 'be-fr' ) {
        newsletterZipcodeField.attr('minlength', '4');
        newsletterZipcodeField.attr('pattern', '[0-9]{4,}');
        newsletterZipcodeField.attr(':class', "{'validate peer': zipcode !== null && zipcode.length >= 4}");
    }
    if ( newsletterZipcodeLang == 'de' ) {
        newsletterZipcodeField.attr('minlength', '5');
        newsletterZipcodeField.attr('pattern', '[0-9]{5,}');
        newsletterZipcodeField.attr(':class', "{'validate peer': zipcode !== null && zipcode.length >= 5}");
    }
    if ( newsletterZipcodeLang == 'nl' ) {
        newsletterZipcodeField.attr('minlength', '6');
        newsletterZipcodeField.attr('pattern', '[1-9][0-9]{3} ?(?!sa|sd|ss)[a-zA-Z]{2}');
        newsletterZipcodeField.attr(':class', "{'validate peer': zipcode !== null && zipcode.length >= 6}");
    }
}

var usZip = $('#form_default form.form-lang-us').find('input[name="postcode"]');
if ( usZip.length > 0 ) {
    usZip.on('input', function(){
        var value = usZip.val().replace(/[^0-9]/g, ''); // Alleen cijfers behouden

        if ( value.length > 5 ) {
            if ( value.length <= 9 ) {
                // Voeg streepje toe tussen eerste 5 en laatste cijfers
                value = value.slice(0, 5) + '-' + value.slice(5);
            } else {
                // Beperk tot maximaal 9 cijfers (5+4)
                value = value.slice(0, 5) + '-' + value.slice(5, 9);
            }
        }

        usZip.val(value);
    });
}

$('button#sendExtraLead').on('click', function(){
    $referrer = $(this).data('referrer');
    $leadtype = $(this).data('leadtype');
    $this = $(this);

    $.request('onSendExtraLead', {
        data: {
            referrer: $referrer,
            leadtype: $leadtype
        },
        success: function(data) {
            if ( data.valid ) {
                $('#success_alert').show();
                $('#extra_lead_buttons').hide();
                $this.prop('disabled', true).attr('disabled', true);
                setTimeout(function() {
                    $('.popup-close-after-action .close-popup').trigger('click');
                }, 2000);
            }
        }
    });

});

$('button#declineExtraLead').on('click', function(){
    $('#decline_alert').show();
    $('#extra_lead_buttons').hide();
});

$('#form_default').on('change', 'input, select, textarea', function() {
    var $sessionName = 'saved_form_data';
    var $name = $(this).prop('name');
    var $value = $(this).val();
    var $data = {};
    $data[$name] = $value;
    $.request('onSetFormDataSession', {
        data: {
            sessionName: $sessionName,
            data: $data
        },
        success: function(data) {
            if ( data.valid ) {
                console.log('Form data saved');
            }
        }
    });
});

$('#form-2025 #form_default').on('focus click input change', 'input, select, textarea, .form-btn-next', function() {
     var bodyElement = document.body;
    if (bodyElement && bodyElement._x_dataStack && bodyElement._x_dataStack[0]) {
        bodyElement._x_dataStack[0].formFocus = true;
    }
});
