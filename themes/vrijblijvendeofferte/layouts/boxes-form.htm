name = "Formulier pagina"
description = "Boxes formulier pagina"

[renderForm NewsletterForm]
formCode = "nieuwsbrief"

[renderForm PartnerForm2]
formCode = "contact-form-2"

[renderForm NewsletterSnippet]
formCode = "nieuwsbrief"

[categories]

[cookiesBar]

[Testing]

[SiteSettings]
==
<?php
use Vbo\Settings\Classes\Visitor;
use Vbo\Settings\Classes\ValidateMail;
use Vbo\Otp\Controllers\OneTimePasswordController;
use Carbon\Carbon;
use October\Rain\Network\Http;
function onStart()
{
    if (post('zipcode') != '' ) {
        Session::put('zipcode', post('zipcode'));
    }
    if (post('housenumber') != '' ) {
        Session::put('housenumber', post('housenumber'));
    }
    Session::put('addition', post('addition'));
    if (post('streetname') != '' ) {
        Session::put('streetname', post('streetname'));
    }
    if (post('city') != '' ) {
        Session::put('city', post('city'));
    }

    $this['zipcode'] = Session::get('zipcode');
    $this['housenumber'] = Session::get('housenumber');
    $this['addition'] = Session::get('addition', $default = null);
    $this['streetname'] = Session::get('streetname');
    $this['city'] = Session::get('city');
    $this['http'] = "https://{$_SERVER['HTTP_HOST']}";
    $this['naw'] = Session::get('NAW');
    $this['responsehash'] = Session::get('responseHash');
    $this['thankyoupage'] = Input::get('bedankt');

    if ( Session::has('prelander') && Session::get('prelander') == true ) {
        $this['landingpage'] = Session::get('landingpage_first');
        $this['ua_code'] = Session::get('ua_code');
    } else {
        $this['landingpage'] = Request::server('HTTP_HOST') . Request::server('REQUEST_URI');
        $this['ua_code'] = Request::get('ua');
        Session::put('landingpage', Request::server('HTTP_HOST') . Request::server('REQUEST_URI'));
        Session::put('ua_code', Request::get('ua'));
    }

    if ( Session::has('mortgageCalculation') ) {
        $this['berekening'] = json_decode( stripslashes( Session::get('mortgageCalculation') ), true);

        $vars = array( 'moving_budget_max', 'moving_budget_desired', '50_percent_grace', 'annuity_remainder', 'annuity_calculation', 'annuity_calculation_correction', 'grace_calculation', 'maximum_loan', 'maximum_loan_correction', 'gross_monthly_costs', 'annuity_monthly_costs_maximum_mortgage', 'gross_monthly_costs_maximum_mortgage', 'annuity_monthly_costs_desired_mortgage', 'gross_monthly_costs_desired_mortgage', 'gross_monthly_costs_desired_mortgage_correction', 'savings_per_year', 'savings_per_year_1.5', 'desired_mortgage', 'age1', 'age2', 'is_single', 'total_income', 'total_income_for_expense_rate', 'selected_expense', 'expense_type', 'expense_rate', 'calculation', 'grace_monthly_costs_maximum_mortgage_1.5', 'annuity_monthly_costs_maximum_mortgage_1.5', 'gross_monthly_costs_maximum_mortgage_1.5', 'grace_monthly_costs_desired_mortgage_1.5', 'annuity_monthly_costs_desired_mortgage_1.5', 'gross_monthly_costs_desired_mortgage_1.5', 'gross_monthly_costs_desired_mortgage_1.5_correction', 'hypotheek_haalbaarheid', 'besparing', 'maximum_withdrawal', 'opname', 'gross_monthly_costs_maximum_mortgage_correction', 'gross_monthly_costs_maximum_mortgage_1.5_correction');

        $searchTags = json_decode( stripslashes( Session::get('mortgageCalculation') ), true);
        if ( $searchTags ) {
            foreach ($searchTags as $tag => $value) {
                if (!in_array($tag, $vars)) {
                    unset($searchTags[$tag]);
                }
            }
        }

        $this['search_tags'] = $searchTags;
    }
}
function onEnd()
{
    $visitor = new Visitor;
    $this['uid_code'] = $visitor->getVisitor();
}
function onCheckMail()
{
    $checkMail = new ValidateMail;
    $validmail = $checkMail->check(post('email'));
    return $validmail;
}
function onCalculateAge()
{
    $date = post('date');
    if (empty($date)) {
        return '';
    }
    if (!preg_match('/^\d{2}-\d{2}-\d{4}$/', $date)) {
        return '';
    }

    try {
        $carbon = Carbon::createFromFormat('d-m-Y', $date);

        if (!$carbon || $carbon->format('d-m-Y') !== $date) {
            return '';
        }

        return (string) $carbon->age;
    } catch (\Exception $e) {
        Log::warning('Invalid date format in onCalculateAge: ' . $date);
        return '';
    }
}

function onSendOtp()
{
    $otpController = new OneTimePasswordController(
        sessionId: input('sessionId'),
        recipient: input('recipient'),
    );
    $otpController->sendOneTimePassword();
    return;
}

function onValidateOtp()
{
    $otpController = new OneTimePasswordController(
        sessionId: input('sessionId'),
        recipient: input('recipient'),
    );
    try {
        $verificationResult = $otpController->verifyOneTimePassword(input('otpCode'));

        if ($verificationResult) {
            if ( input('location') == 'thankyoupage_otp' ) {
                Session::put('otp_verified', true);
                $updateLeadDestination = config('vbo.dashboard_api') . 'otp/' . input('email');
                $updateLead = Http::get($updateLeadDestination);
                $updatePhoneDestination = config('vbo.dashboard_api') . input('email') . '/phone-correction/' . input('recipient');
                $updatePhone = Http::get($updatePhoneDestination);
            }
            return [
                'valid' => true
            ];
        } else {
            Log::info('Verification of code ' . input('otpCode') . ' for telephone number ' . input('recipient') . ' has failed!');
            return [
                'valid' => false
            ];
        }

    } catch (Throwable $e) {
        Log::info('An error occurred during verification: ' . $e->getMessage());
    }
}

function onSendExtraLead()
{
    $referrer = post('referrer');
    $leadtype = post('leadtype');
    $destination = config('vbo.lead_processor_url');
    $responseHashCodeExtraLead = 0;

    $data = Session::get('lead_data');
    $data['lead_type'] = $leadtype;

    if ( !$data ) {
        return [
            'valid' => false
        ];
    }

    try {
        $response = Http::post($destination, function ($http) use ($referrer, $data) {
            $http->requestOptions = [ CURLOPT_FOLLOWLOCATION => false, ];
            $http->header('Referer', $referrer);
            $http->header('Origin', "https://{$_SERVER['HTTP_HOST']}");
            $http->data($data);
        });

        if ( array_key_exists('location', $response->headers) ) {
            $responseLocationExtraLead = $response->headers['location'];

            // Parse "x" hash from "location" header.
            if ($responseLocationExtraLead) {
                if (preg_match('/(?:\?|&)x=([^\/]+)/i', $responseLocationExtraLead, $matches)) {
                    $responseHashCodeExtraLead = $matches[1];
                }
            }

            if (Session::has('responseHashCodeExtraLead')) {
                Session::forget('responseHashCodeExtraLead');
            }
            Session::put('responseHashCodeExtraLead', $responseHashCodeExtraLead);
        } else {
            // $this->sendErrorMessage($data);
        }

    } catch ( \Exception $e ) {
        // $this->sendErrorMessage($data);
        // Log::info('error exception: ' . $e->getMessage());
    }

    return [
        'valid' => true
    ];
}

function onSetFormDataSession()
{
    $sessionName = post('sessionName');
    $data = post('data');
    $valid = false;
    if ( $sessionName != '' && $data !== null ) {
        Session::put($sessionName, $data);
        $valid = true;
    }
    return [
        'valid' => $valid
    ];
}

function onGetFormDataSession()
{
    $sessionName = post('sessionName');
    $data = null;
    $valid = false;
    if ( $sessionName != '' ) {
        $data = Session::get($sessionName, []);
        $valid = true;
    }
    return [
        'valid' => $valid,
        'data' => $data
    ];
}

?>
==
<!doctype html>
<html lang="{{ site_lang|default('nl') }}" class="scroll-smooth">
    <head>
        {% partial "site/head" %}
        {% styles %}
    </head>

    <body class="antialiased font-inter bg-gray-100" x-data="{ formFocus: false, ctaBanner: false, hasSeenForm: false, scrolled: 0, formEnd: 0 }" :class="{ 'formFocussed': formFocus }" @scroll.window="scrolled = window.pageYOffset || (document.documentElement || document.body.parentNode || document.body).scrollTop" @touchmove.window="scrolled = window.pageYOffset || (document.documentElement || document.body.parentNode || document.body).scrollTop">
        {% partial "site/scripts_body_top" %}

        {% if not boxesPage.custom_config.hide_lenen_banner %}
            {% if this.theme.geld_lenen_banner %}
            <div class="py-2 bg-white flex w-full justify-center px-4 md:px-8">
                <img src="{{ this.theme.geld_lenen_banner|media }}" alt="Let op: Geld lenen kost geld" class="max-w-full">
            </div>
            {% endif %}
        {% endif %}

        {% partial "page/navbar_no_menu" %}

       <main class="relative overflow-hidden bg-white" id="mainsection">
        {% ajaxPartial 'site/page-replace' %}
       </main>

        {% partial 'page/cta-banner' %}

        {% partial "page/footer" %}

        {% if not this.theme.hide_cookiebanner %}{% component 'cookiesBar' %}{% endif %}

        {% partial "site/foot" %}

        {% component 'Testing' %}

        {% scripts %}
        {% framework extras %}

         <script>
            var lazyLoadInstance = new LazyLoad({});
        </script>
    </body>
</html>
