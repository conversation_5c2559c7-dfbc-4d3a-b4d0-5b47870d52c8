<div class="relative bg-[#F5F5F5] md:bg-gray-100 wrapper" id="form-2025" x-data="{ formContainerTouchesTop: false }">
    {% partial 'atomic/molecules/form-image' %}

    <div class="relative md:pt-4">
        <div class="{{ not box.mobileTags ? 'pb-6 md:pb-0' }} md:pt-8 lg:py-16 {{ box.title_above_form ? 'lg:pb-24' }}">
            <div class="container relative md:px-8 xl:px-4 transition-all ease-in duration-200  md:mb-8 lg:mb-0" id="hero-2025-content">
                {% partial 'atomic/molecules/form-hero-title' %}
                {% partial 'atomic/molecules/form-usps' %}
            </div>
        </div>

        <div id="hero-2025-formcontainer" class="container {{ box.title_above_form ? 'pt-12 md:pt-16' }} lg:pt-0">

            <div class="md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-4 lg:gap-8  md:px-4 xl:px-0">
                <div class="md:col-span-2">
                    <div
                        id="form_wrapper"
                        class="{{ box.title_above_form ? 'formHasTitleAbove' }} bg-white rounded-lg p-6 md:p-12 md:pb-20 {{ box.veiligeVerbinding ? 'pb-20' : 'pb-12' }} md:relative md:top-auto mb-10 md:mb-6 lg:mb-16 relative z-20 shadow-form md:shadow-formxl"
                        x-trap.noscroll.inert.noautofocus.noreturn="formFocus"
                        {% if not input('bedankt') %}@mousedown="formFocus = true" {% endif %}
                    >
                        <div
                            id="form_default"
                            x-intersect:enter="hasSeenForm = true, ctaBanner = false"
                            x-intersect:leave="if (hasSeenForm && scrolled > formEnd) ctaBanner = true; if (formEnd === 0) formEnd = window.pageYOffset || (document.documentElement || document.body.parentNode || document.body).scrollTop"
                        >

                            {% if box.title_above_form %}
                                {% partial 'atomic/molecules/form-avatar' %}
                            {% endif %}

                            {# The Form #}
                            {% ajaxPartial 'site/dynamic-form' formcode=box.formcode %}

                        </div>

                        {% partial 'atomic/molecules/form-footer' %}
                    </div>

                    <div class="hidden md:block fixed inset-0 bg-black/30 z-10" x-show="formFocus" @click="formFocus = false" x-cloak></div>
                </div>

                <div class="md:col-span-2 lg:col-span-1 md:grid md:grid-cols-2 md:gap-8 lg:block" id="hero-2025-sidebar">
                    {% partial 'atomic/molecules/form-sidebar' %}
                </div>
            </div>

            {% partial 'atomic/molecules/form-reviews' %}
        </div>
    </div>
</div>
